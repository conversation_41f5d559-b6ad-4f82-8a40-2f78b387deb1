# PowerShell script to convert Chapter 13 questions from MD to TEX format

# Function to convert single choice questions
function Convert-SingleChoice {
    param($inputFile, $outputFile)
    
    $content = Get-Content $inputFile -Raw -Encoding UTF8
    
    # Extract question content (skip metadata)
    $lines = $content -split "`n"
    $questionStart = -1
    $answerStart = -1
    
    for ($i = 0; $i -lt $lines.Length; $i++) {
        if ($lines[$i] -match "^---$" -and $questionStart -eq -1) {
            continue
        }
        if ($lines[$i] -match "^---$" -and $questionStart -eq -1) {
            $questionStart = $i + 1
            continue
        }
        if ($lines[$i] -match "^\*\*参考答案：\*\*") {
            $answerStart = $i
            break
        }
    }
    
    if ($questionStart -eq -1) {
        $questionStart = 6  # Default start after metadata
    }
    
    # Extract question text and choices
    $questionText = ""
    $choices = @()
    $answer = ""
    
    for ($i = $questionStart; $i -lt $answerStart; $i++) {
        if ($lines[$i] -match "^[A-D]\." -or $lines[$i] -match "^[A-D]\.") {
            # This is a choice line
            $choiceText = $lines[$i] -replace "^[A-D]\.\s*", ""
            $choices += $choiceText.Trim()
        } else {
            # This is question text
            if ($lines[$i].Trim() -ne "") {
                $questionText += $lines[$i].Trim() + " "
            }
        }
    }
    
    # Extract answer
    if ($answerStart -ne -1 -and $answerStart + 1 -lt $lines.Length) {
        $answer = $lines[$answerStart + 1].Trim()
    }
    
    # Clean up question text and add answer marker
    $questionText = $questionText.Trim()
    if ($questionText -match "\(\s*\)") {
        $questionText = $questionText -replace "\(\s*\)", "\paren[$answer]"
    } else {
        $questionText += "\paren[$answer]"
    }
    
    # Generate TEX content
    $texContent = @"
\begin{question}
$questionText
\begin{choices}
"@
    
    foreach ($choice in $choices) {
        $texContent += "`n\item $choice"
    }
    
    $texContent += @"

\end{choices}
\end{question}

\iffalse
文字解析
\fi

\iffalse
音频解析
\fi

\iffalse
视频解析
\fi
"@
    
    # Write to output file
    $texContent | Out-File -FilePath $outputFile -Encoding UTF8
}

# Function to convert fill-in-the-blank questions
function Convert-FillBlank {
    param($inputFile, $outputFile)
    
    $content = Get-Content $inputFile -Raw -Encoding UTF8
    $lines = $content -split "`n"
    
    # Find question and answer
    $questionText = ""
    $answers = @()
    
    $inQuestion = $false
    $inAnswer = $false
    
    foreach ($line in $lines) {
        if ($line -match "^---$") {
            continue
        }
        if ($line -match "^id:|^chapter:|^questionType:") {
            continue
        }
        if ($line -match "^\*\*参考答案：\*\*") {
            $inAnswer = $true
            $inQuestion = $false
            continue
        }
        
        if (-not $inAnswer -and $line.Trim() -ne "") {
            $questionText += $line.Trim() + " "
        }
        
        if ($inAnswer -and $line.Trim() -ne "") {
            $answers += $line.Trim() -split "、|，|,"
        }
    }
    
    # Replace blanks with \fillin*[answer]
    $questionText = $questionText.Trim()
    $answerIndex = 0
    
    while ($questionText -match "___" -and $answerIndex -lt $answers.Length) {
        $answer = $answers[$answerIndex].Trim()
        $questionText = $questionText -replace "___", "\fillin*[$answer]", 1
        $answerIndex++
    }
    
    # Generate TEX content
    $texContent = @"
\begin{question}
$questionText
\end{question}

\begin{solution}

\end{solution}

\iffalse
文字解析
\fi

\iffalse
音频解析
\fi

\iffalse
视频解析
\fi
"@
    
    $texContent | Out-File -FilePath $outputFile -Encoding UTF8
}

# Main conversion process
$sourceDir = "题目拆分/第十三章_浅基础设计基本原理"
$targetDir = "TEX/第十三章_浅基础设计基本原理"

# Create target directories
$questionTypes = @("单项选择题", "多项选择题", "填空题", "计算选择案例题", "计算题")

foreach ($type in $questionTypes) {
    $targetPath = Join-Path $targetDir $type
    if (-not (Test-Path $targetPath)) {
        New-Item -ItemType Directory -Path $targetPath -Force
    }
}

Write-Host "Starting conversion of Chapter 13..."

# Convert single choice questions
Write-Host "Converting single choice questions..."
$singleChoiceFiles = Get-ChildItem "$sourceDir/单项选择题/*.md"
foreach ($file in $singleChoiceFiles) {
    $outputFile = "$targetDir/单项选择题/$($file.BaseName).tex"
    Convert-SingleChoice $file.FullName $outputFile
    Write-Host "Converted: $($file.Name)"
}

Write-Host "Conversion completed!"
